import { z } from "zod";

export const AvailableModel = [
  "openai/gpt-4.1",
  "openai/gpt-4.1-mini",
  "openai/gpt-4.1-nano",
  "openai/o4-mini",
  "openai/o3",
  "openai/o3-mini",
  "openai/o1",
  "openai/o1-mini",
  "openai/gpt-4o",
  "openai/gpt-4o-mini",
  "openai/gpt-4o-2024-08-06",
  "openai/gpt-4.5-preview",
  "openai/o1-preview",
  "anthropic/claude-3-5-sonnet-latest",
  "anthropic/claude-3-5-sonnet-20241022",
  "anthropic/claude-3-5-sonnet-20240620",
  "anthropic/claude-3-7-sonnet-latest",
  "anthropic/claude-3-7-sonnet-20250219",
  "cerebras/cerebras-llama-3.3-70b",
  "cerebras/cerebras-llama-3.1-8b",
  "groq/groq-llama-3.3-70b-versatile",
  "groq/groq-llama-3.3-70b-specdec",
  "google/gemini-1.5-flash",
  "google/gemini-1.5-pro",
  "google/gemini-1.5-flash-8b",
  "google/gemini-2.0-flash-lite",
  "google/gemini-2.0-flash",
  "google/gemini-2.5-flash-preview-04-17",
  "google/gemini-2.5-pro-preview-03-25",
] as const;

export const AvailableModelSchema = z.enum(AvailableModel);
