# EcommerceScrapingFlow - Clean Diagram

```mermaid
flowchart TD
    %% Styling for better visibility
    classDef startClass fill:#4CAF50,stroke:#2E7D32,color:#fff,stroke-width:2px
    classDef processClass fill:#2196F3,stroke:#1976D2,color:#fff,stroke-width:2px
    classDef decisionClass fill:#FF9800,stroke:#F57C00,color:#fff,stroke-width:2px
    classDef successClass fill:#4CAF50,stroke:#2E7D32,color:#fff,stroke-width:2px
    classDef errorClass fill:#F44336,stroke:#D32F2F,color:#fff,stroke-width:2px
    classDef retryClass fill:#FFC107,stroke:#FFA000,color:#000,stroke-width:2px
    classDef endClass fill:#9E9E9E,stroke:#616161,color:#fff,stroke-width:2px

    %% Main Flow
    A[🚀 Initialize Scraping<br/>@start] --> B{🧭 Navigate & Prepare<br/>@listen}
    B -->|Success| C{📦 Extract Products<br/>@listen}
    B -->|Error| H[❌ Handle Error<br/>@listen]
    
    C -->|Success| D{🔍 Validate & Route<br/>@router}
    C -->|Error| H
    
    %% Validation Decision Branch
    D -->|✅ Validation Passed| E{📄 Has More Pages?}
    D -->|❌ Validation Failed| F{🔄 Retries < Max?}
    D -->|Error| H
    
    %% Success Path
    E -->|Yes| G[📄 Navigate Next Page<br/>@listen]
    E -->|No| I[🎉 Finalize Results<br/>@listen]
    G --> B
    
    %% Retry Path
    F -->|Yes| J[🔄 Re-extract with Feedback<br/>@listen]
    F -->|No| E
    J --> C
    
    %% End States
    I --> K[🏁 End]
    H --> K
    
    %% Apply styling
    class A startClass
    class B,C,D,E,F processClass
    class G,J retryClass
    class I successClass
    class H errorClass
    class K endClass

    %% Add flow labels - Success: green, Error: red, Retry: yellow, Process: blue
    linkStyle 0 stroke:#4CAF50,stroke-width:3px
    linkStyle 1 stroke:#4CAF50,stroke-width:3px
    linkStyle 2 stroke:#F44336,stroke-width:3px
    linkStyle 3 stroke:#4CAF50,stroke-width:3px
    linkStyle 4 stroke:#F44336,stroke-width:3px
    linkStyle 5 stroke:#4CAF50,stroke-width:3px
    linkStyle 6 stroke:#FFC107,stroke-width:3px
    linkStyle 7 stroke:#F44336,stroke-width:3px
    linkStyle 8 stroke:#2196F3,stroke-width:3px
    linkStyle 9 stroke:#4CAF50,stroke-width:3px
    linkStyle 10 stroke:#2196F3,stroke-width:3px
    linkStyle 11 stroke:#FFC107,stroke-width:3px
    linkStyle 12 stroke:#2196F3,stroke-width:3px
    linkStyle 13 stroke:#2196F3,stroke-width:3px
    linkStyle 14 stroke:#9E9E9E,stroke-width:3px
    linkStyle 15 stroke:#9E9E9E,stroke-width:3px
```

## Flow Description

### 🚀 **Initialize Scraping** (@start)
- Sets up the scraping session
- Resets state variables (current_page = 1)
- Prepares initial navigation parameters

### 🧭 **Navigate & Prepare** (@listen)
- Uses NavigationAgent to handle popups and page preparation
- Shares browser session across all agents
- Returns success/error status

### 📦 **Extract Products** (@listen)
- Uses ExtractionAgent to grab product data
- Reuses the same browser session
- Adds extracted products to state

### 🔍 **Validate & Route** (@router)
- Uses ValidationAgent to check data quality
- **Router Logic:**
  - ✅ **Validation Passed** → Check for more pages
  - ❌ **Validation Failed** → Check retry attempts
  - 🚨 **Error** → Handle error

### 📄 **Has More Pages?** (Decision)
- **Yes** → Navigate to next page and continue loop
- **No** → Complete the scraping process

### 🔄 **Retries < Max?** (Decision)
- **Yes** → Re-extract with validation feedback
- **No** → Move to next page (skip current page)

### 📄 **Navigate Next Page** (@listen)
- Handles pagination navigation
- Loops back to Navigate & Prepare

### 🔄 **Re-extract with Feedback** (@listen)
- Uses validation feedback to improve extraction
- Loops back to Extract Products

### 🎉 **Finalize Results** (@listen)
- Converts products to StandardizedProduct objects
- Calculates statistics and success rates
- Prepares final output

### ❌ **Handle Error** (@listen)
- Logs error details
- Returns error result with partial data

### 🏁 **End**
- Process completion (success or error)

## Key Features

1. **Session Sharing**: All agents use the same browser session for efficiency
2. **Feedback Loops**: Failed extractions can retry with validation feedback
3. **Error Handling**: Graceful error handling at every step
4. **State Management**: Centralized state tracking throughout the process
5. **Modular Design**: Each step uses specialized agents for specific tasks 