{"name": "@browserbasehq/mcp-server-browserbase", "version": "2.0.0", "description": "MCP server for AI web browser automation using Browserbase and Stagehand", "license": "Apache-2.0", "author": "Browserbase, Inc. (https://www.browserbase.com/)", "homepage": "https://www.browserbase.com", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "main": "./cli.js", "module": "./src/index.ts", "bin": {"mcp-server-browserbase": "cli.js"}, "files": ["assets", "README.md", "dist", "cli.js", "index.d.ts", "index.js", "config.d.ts"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "husky && npm run build", "watch": "tsc --watch", "smithery": "npx @smithery/cli dev src/index.ts", "inspector": "npx @modelcontextprotocol/inspector build/index.js", "lint": "eslint . --ext .ts", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "clean": "rm -rf dist", "prepublishOnly": "pnpm clean && pnpm build", "pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@browserbasehq/sdk": "^2.6.0", "@browserbasehq/stagehand": "^2.4.0", "@modelcontextprotocol/sdk": "^1.13.1", "@smithery/cli": "^1.2.15", "commander": "^14.0.0", "dotenv": "^16.4.6", "playwright-core": "^1.53.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.1", "shx": "^0.3.4", "tsx": "^4.20.3", "typescript": "^5.6.2", "typescript-eslint": "^8.35.0"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}